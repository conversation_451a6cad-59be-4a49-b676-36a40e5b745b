@if (isset($socialPost))
    <div class="form-border">
        <style>
            .socialComp {
                display: flex;
                justify-content: center;
                align-items: center;
                gap: 16px;
                margin-top: 30px!important;
            }

            .socialComp > div {
                flex: 1 1 0;
                text-align: center;
            }
        </style>
        <div class="row">
            <div class="col-xl-12 col-md-12 col-12">
                <div class="form-group">
                    <div class="imageNvideo">
                        @if (str_contains($socialPost->link, 'http'))
                            <a class="view-link" href="{{ $socialPost->link }}" target="_blank">View</a>
                        @else
                            @if ($socialPost->type == 'photo')
                                <a href="{{ asset('storage/' . $socialPost->link) }}" target="_blank">
                                    <img src="{{ asset('storage/' . $socialPost->link) }}" alt="Social post photo" style="max-width:100%; height:auto;">
                                </a>
                            @elseif($socialPost->type == 'video')
                                <video controls style="max-width:100%; height:auto;">
                                    <source src="{{ asset('storage/' . $socialPost->link) }}" type="video/mp4">
                                    Your browser does not support the video tag.
                                </video>
                            @endif
                        @endif
                    </div>
                </div>
            </div>
            <div class="col-xl-12 col-md-12 col-12">
                <div class="ssn">
                    <div class="socialdetail">
                        <div class="socialimage">
                            <img src="{{ asset('assets/front-end/images/icons/campaigns-' . $socialPost->media . '.svg') }}" alt="{{ $socialPost->media }} icon">
                        </div>
                        <div class="socialcontent">
                            <div class="socialcontent-posttype">
                                <span class="m-0">Share Content from Brand - Post/Story</span>
                            </div>
                            <div class="socialcontent-postlink">
                                @if (str_contains($socialPost->link, 'http'))
                                    <a class="view-link" style="width:auto;" href="{{ $socialPost->link }}" target="_blank">{{ $socialPost->link }}</a>
                                @else
                                    <a class="view-link" style="width:auto;" href="{{ $socialPost->thumbnail }}" target="_blank">{{ $socialPost->thumbnail }}</a>
                                @endif
                            </div>
                        </div>
                    </div>
                    <x-insight-stats :influencer-request-detail="$influencerRequestDetail" />
                </div>
            </div>
        </div>
    </div>
@endif
