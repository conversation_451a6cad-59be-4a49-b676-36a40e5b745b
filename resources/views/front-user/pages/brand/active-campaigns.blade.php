@extends('front-user.layouts.master_user_dashboard')

@section('content')
    <link rel="stylesheet" href="{{ asset('/assets/front-end/css/brand/active-campaigns.css') }}">
    <section id="campaignForm">
        <h1 class="section-heading"><span>Active Campaigns</span></h1>
        <div class="row">
            <div class="offset-md-8 col-md-4 col-sm-6 section-button">
                <a href="{{ url('campaign-history') }}" class="btn btn-outline-secondary top-right-button btn-history" style="float: right;">
                    <img src="{{ asset('/assets/front-end/images/new/history.svg') }}"> Campaign History
                </a>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12 new_card_row">
                @if (isset($influencerCampaignDetails))
                    @foreach ($influencerCampaignDetails as $influencerCampaignDetail)
                        @if ($influencerCampaignDetail->invoice_id != null)
                            @if ($influencerCampaignDetail->finish != '1' && $influencerCampaignDetail->status != 'Cancelled')
                                {{-- Desktop View --}}
                                <div class="campaign-card desktop-view">
                                    @include('front-user.pages.desktop.brand.active-campaigns', [
                                        'influencerCampaignDetail' => $influencerCampaignDetail,
                                        'activeCampaignsCount' => $activeCampaignsCount
                                    ])
                                </div>

                                {{-- Mobile View --}}
                                <div class="campaign-card mobile-view" style="display: none;">
                                    @include('front-user.pages.mobile.brand.active-campaigns', [
                                        'influencerCampaignDetail' => $influencerCampaignDetail,
                                        'activeCampaignsCount' => $activeCampaignsCount
                                    ])
                                </div>

                                <x-modals.brand.active-campaigns.campaign-details-modal :influencerCampaignDetail="$influencerCampaignDetail" />

                                @foreach ($influencerCampaignDetail->influencer_request_details as $influencerRequestDetail)
                                    @include('front-user.pages.market-step-detail-influencer', ['influencerSocialLinkDetail' => $influencerRequestDetail->social_link])

                                    {{-- Influencer show results --}}
                                    <x-modals.influencer.influencer-show-results :influencerRequestDetail="$influencerRequestDetail" />

                                    {{-- Review influencer submission and rate influencer Modal --}}
                                    <x-modals.brand.active-campaigns.review-rating-modal :influencerRequestDetail="$influencerRequestDetail" />
                                    {{-- Review Rating Popup Modal --}}
                                    <x-modals.brand.active-campaigns.review-rating-popup-modal :influencerRequestDetail="$influencerRequestDetail" />
                                    {{-- Complaint Popup Modal --}}
                                    <x-modals.brand.active-campaigns.complaint-popup-modal :influencerRequestDetail="$influencerRequestDetail" />
                                    {{-- Complaint Popup Confirm Modal --}}
                                    <x-modals.brand.active-campaigns.complaint-popup-confirm-modal :influencerRequestDetail="$influencerRequestDetail" />

                                    {{-- Complaint Submit Modal --}}
                                    <x-modals.brand.active-campaigns.complaint-submit-modal :influencerRequestDetail="$influencerRequestDetail" />
                                @endforeach
                            @endif
                        @endif
                    @endforeach
                @endif

                @if ($activeCampaignsCount == 0)
                    <div class="no-data-div">
                        <img src="{{ asset('/assets/front-end/images/icons/icon-no-data-check.png') }}" class="image2"
                            alt="">
                        <div class="no-data-contant">
                            You currently have no running campaigns
                        </div>
                        <div class="">
                            <a href="{{ url('create-campaign') }}" class="button-ccg">Create campaign</a>
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <div class="loaderss" id="pageLoader">
            <img src="{{ asset('/assets/front-end/images/loading-loading-forever.gif') }}" alt="">
        </div>
    </section>
@endsection

{{-- Global single modals --}}
<x-modals.brand.campaign-phases />
<x-modals.brand.active-campaigns.review-success-modal />
<x-modals.brand.active-campaigns.complaint-confirm-modal />

@section('script_links')
    <script src="https://js.mollie.com/v1/mollie.js"></script>
    <script src="https://js.stripe.com/v3/"></script>
@endsection

@section('script_codes')
    <script>
        // Pass PHP variables to JavaScript
        var complaint = @json($complaint);
        var review = @json($review);
        var user = @json(@$review->influencer_request_accepts->user ?? null);
        var baseUrl = "{{ URL::to('/') }}";
        var stripeKey = "{{ env('STRIPE_KEY') }}";
    </script>
    <script src="{{ asset('/assets/front-end/js/brand/active-campaigns.js') }}"></script>
@endsection

