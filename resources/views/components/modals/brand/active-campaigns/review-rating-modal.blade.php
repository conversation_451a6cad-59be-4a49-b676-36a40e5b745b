<!--modal reviewRating form -->
<div class="modal fade influencer" id="reviewRating{{ $influencerRequestDetail->id }}"
    data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
    aria-labelledby="reviewRatingLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-body">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    <img src="{{ asset('/assets/front-end/images/icons/close-one.svg') }}" alt="Close">
                </button>
                <div class="wizardHeading">Confirm Submit</div>
                <div class="wizardForm">
                    <input
                        type="hidden"
                        name="influencer_request_accept_id"
                        id="influencer_request_accept_id"
                        value="{{ $influencerRequestDetail->influencer_request_accepts->id }}">
                    <div class="col-xl-12 col-md-12 col-12 campHistory">
                        @include('front-user.pages.review-influencer-submission')
                    </div>
                    <div class="form-border">
                        <div class="row">
                            <div class="col-12">
                                <div class="bold-text">Please confirm if the influencer
                                    have fullfilled all the following tasks:</div>
                                <div class="tasklists{{ $influencerRequestDetail->id }}">
                                    @include('front-user.pages.influencer-tasks')
                                </div>
                            </div>
                        </div>
                    </div>

                    @if ($influencerRequestDetail->post_type == 'Survey')
                        <div class="form-border">
                            <div class="row">
                                <div class="col-sm-8"
                                    style="display: flex; align-items: center;">
                                    You can download your survey results here:
                                </div>
                                <div class="col-sm-4">
                                    <button
                                        class="btn btn-show-survey-results"
                                        onclick="downloadImage('{{ asset('storage/' . $influencerRequestDetail->insight) }}')"
                                        style="width: auto; padding: .375rem .75rem; height: auto; border-radius: 5px;">
                                        <i class="bi bi-download"></i>
                                        Survey Results
                                    </button>
                                </div>
                            </div>
                        </div>
                    @endif

                    <input type="hidden" id="review_count{{ $influencerRequestDetail->id }}" value="0">
                    <input type="hidden" id="min_count{{ $influencerRequestDetail->id }}" value="0">
                    <br>
                    <!-- Selected users //-->
                    <div class="widthBtnPopup d-flex">
                        <button class="et-submit red-btn mx-4 complant-btn"
                            type="submit" name="reject"
                            id="complaint{{ $influencerRequestDetail->id }}" target="popup"
                            onclick="showComplaint({{ $influencerRequestDetail->id }})">Complaint</button>
                        <input class="et-submit mx-4 ds" type="submit"
                            name="confirm" value="Confirm"
                            id="review{{ $influencerRequestDetail->id }}" target="popup"
                            data-bs-toggle="modal"
                            data-bs-target="#reviewRatingPopup{{ $influencerRequestDetail->id }}">
                    </div>
                </div>
                <!--end  modal complaintPopup   form -->
            </div>
        </div>
    </div>
</div>
<!--end  modal reviewRating form -->
